<p align="center">
    <img src="https://images.gitee.com/uploads/images/2019/0109/214218_d2aa949b_551203.png" width="300">
    <br>      
    <br>      
    <p align="center">
        Gan"肝"是一个应用程序开发框架集，基于主流技术Spring Boot3 ， React ，Flutter ，Taro ，打造了前端后端多个快速开发脚手架，旨在让开发者少"肝"多休息
        <br>
        <br>
        <img src="https://img.shields.io/badge/gan-1.0.0-success.svg" alt="Build Status">
        <a href="http://spring.io/projects/spring-boot">
            <img src="https://img.shields.io/badge/spring--boot-red--3.6.14-green.svg" alt="spring-boot">
        </a>
        <a href="http://mp.baomidou.com">
            <img src="https://img.shields.io/badge/mybatis--plus-3.5.3.1-blue.svg" alt="mybatis-plus">
        </a>  
        <a href="https://www.hutool.cn/">
            <img src="https://img.shields.io/badge/hutool-5.8.16-blue.svg" alt="hutool">
        </a>
        <a href="http://ibeetl.com/">
            <img src="https://img.shields.io/badge/beetl-3.3.1-yellow.svg" alt="beetl">
        </a>  
    </p>
</p>

-----------------------------------------------------------------------------------------------

## 项目介绍
| 依赖                          | 版本         |
| ----------------------------- | ------------- |
| Spring Boot                    | 3.6.14        |
| Spring Security             | 5.6.1          |
| Spring Data JPA               | 3.6.14        |
| Spring Data Redis             | 3.6.14        |
| MyBatis-Plus                  | 3.5.3.1       |
| Hutool                        | 5.8.16        |
| MyBatis-Plus Generator     | 3.5.3.1       |
