## api doc 模块提供api文档日志集成

使用 openapi3 + knife4j 方案

openapi3 参考 https://springdoc.org/
knife4j 参考 https://doc.xiaominfo.com/

### 简单接入说明
#### 引入依赖
```xml
<dependency>
    <groupId>cn.e71</groupId>
    <artifactId>gan-kernel-doc</artifactId>
    <version>1.0.0</version>
</dependency>
```
#### 配置文件
```yaml
gan:
    kernel:
        doc:
            title: ${spring.application.name}-API文档
            version: 1.0.0
            description: ${spring.application.name}-API文档
            base-package: cn.e71
            group: 默认分组
            knife4j-enable: true
            basic-enable : true
            basic-username : e71-admin
            basic-password : 860311
```

#### 如何打开
 http://127.0.0.1:8080/doc.html knife4j文档打开地址
 http://127.0.0.1:8080/swagger-ui.html swagger-ui文档打开地址
 http://127.0.0.1:8080/v3/api-docs swagger文档json格式