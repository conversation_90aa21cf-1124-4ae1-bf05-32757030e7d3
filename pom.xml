<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.e71</groupId>
        <artifactId>gan-kernel</artifactId>
        <version>${revision}</version>
    </parent>
    <groupId>cn.e71</groupId>
    <artifactId>gan-kernel-ssl</artifactId>
    <version>${revision}</version>
    <name>ssl</name>
    <description>add ssl</description>

    <dependencies>


<!--        <dependency>-->
<!--            <groupId>io.undertow</groupId>-->
<!--            <artifactId>undertow-core</artifactId>-->
<!--            <version>${ud.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>io.undertow</groupId>
            <artifactId>undertow-servlet</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>


</project>
