<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.e71</groupId>
        <artifactId>auth</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <groupId>cn.e71</groupId>
    <artifactId>gan-business-auth-api</artifactId>
    <version>${revision}</version>
    <name>api</name>
    <description>api</description>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-api-exception</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>



</project>
