<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.e71</groupId>
        <artifactId>gan-business</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>auth</artifactId>
    <version>${revision}</version>
    <name>auth</name>
    <packaging>pom</packaging>
    <description>auth</description>
    <modules>
        <module>api</module>
        <module>sa-token</module>
        <module>spring-security</module>
        <module>starter</module>
    </modules>

    <dependencyManagement>



        <dependencies>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-business-auth-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-business-auth-sa-token</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-business-auth-spring-security</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

</project>
