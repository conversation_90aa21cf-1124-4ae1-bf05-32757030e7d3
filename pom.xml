<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.e71</groupId>
        <artifactId>gan-business</artifactId>
        <version>${revision}</version>
    </parent>

    <groupId>cn.e71</groupId>
    <artifactId>gan-business-restful-starter</artifactId>
    <version>${revision}</version>
    <name>restful-starter</name>
    <description>restful-starter</description>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>

        <!--集成api文档 -->
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-api-doc</artifactId>
        </dependency>

        <!--集成api基础response实体 -->
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-api-response</artifactId>
        </dependency>

        <!--集成api基础异常 -->
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-api-exception</artifactId>
        </dependency>

        <!--集成日志 -->
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-log</artifactId>
        </dependency>

        <!--集成i18n -->
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-i18n</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!-- 移除掉默认支持的 Tomcat -->
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 使用undertow容器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <dependency>
            <groupId>io.undertow</groupId>
            <artifactId>undertow-servlet</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>




</project>
