<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.e71</groupId>
        <artifactId>gan-examples</artifactId>
        <version>${revision}</version>
    </parent>
    <groupId>cn.e71</groupId>
    <artifactId>gan-examples-auth</artifactId>
    <version>${revision}</version>
    <name>auth-exam</name>
    <description>auth-exam</description>

    <dependencies>
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-business-auth-starter</artifactId>
            <version>${revision}</version>

        </dependency>
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-business-restful-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>


    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <spring.active>local</spring.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <spring.active>dev</spring.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.active>prod</spring.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>






</project>
