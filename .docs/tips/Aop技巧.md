### 通配符：
     “?”：匹配一个字符
     “*”：匹配零个或多个字符
     “**”：匹配路径中的零个或多个目录
     “..”：匹配路径中的0个或多个元素
### 切点表达式:
     execution(public * *(..)) 任意的公共方法
     execution（* set*（..）） 以set开头的所有的方法
     execution（* com.LoggerApply.*（..））com.LoggerApply这个类里的所有的方法
     execution（* com.annotation.*.*（..））com.annotation包下的所有的类的所有的方法
     execution（* com.annotation..*.*（..））com.annotation包及子包下所有的类的所有的方法
     execution(* com.annotation..*.*(String,?,Long)) com.annotation包及子包下所有的类的有三个参数，第一个参数为String类型，第二个参数为任意类型，第三个参数为Long类型的方法
     execution(@annotation(com.xx.annotation.Xannotation)) 注解的形式 切入有改注解的方法
### 本项目实例
     execution(public * cn.e71.*.controller.*.*(..))||execution(public * cn.e71.*.*.controller.*.*(..)) cn.e71下的所有controller包里的方法都切入
     