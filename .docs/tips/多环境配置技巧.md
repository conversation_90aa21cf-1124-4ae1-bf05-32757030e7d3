### Maven pom + spring boot profiles 配置多环境

1. 在pom.xml中添加profiles节点插(properties标签里的spring.active就是我们要引入application里的属性)
```xml
    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <spring.active>local</spring.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <spring.active>dev</spring.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.active>prod</spring.active>
            </properties>
        </profile>
    </profiles>
```
2. 在resources目录下新建application-local.yml、application-dev.yml、application-prod.yml三个文件，分别配置不同的环境

3. 在application.yml中引入profiles
```yml
spring:
  profiles:
    active: @spring.active@
```
4. resources节点加入 filter
```xml
<resource>
    <directory>src/main/resources</directory>
    <filtering>true</filtering>
</resource>
```