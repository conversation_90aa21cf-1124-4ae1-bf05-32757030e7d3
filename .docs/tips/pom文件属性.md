## 一些pom.xml属性记录,方便查阅

依赖范围
maven中三种classpath
编译，测试，运行
1.compile：默认范围，编译测试运行都有效
2.provided：在编译和测试时有效
3.runtime：在测试和运行时有效
4.test:只在测试时有效
5.system:在编译和测试时有效，与本机系统关联，可移植性差

pom.xml常用元素介绍
project 包含pom一些约束的信息
modelVersion 指定当前pom的版本
groupId（主项目标示，定义当前maven属于哪个项目，反写公司网址+项目名）、
artifactId（实际项目模块标识，项目名+模块名）、
version（当前项目版本号，第一个0标识大版本号，第二个0标示分支版本号，第三个0标识小版本号，0.0.1，snapshot快照，alpha内部测试，beta公测，release稳定，GA正式发布）
name项目描述名
url项目地址
description项目描述
developers开发人员列表
licenses许可证
organization：组织
dependencies：依赖列表
dependency：依赖项目 里面放置坐标
scope：包的依赖范围 test
optional ：设置依赖是否可选
exclusions：排除依赖传递列表
dependencyManagement 依赖的管理
build：为构建行为提供支持
plugins：插件列表
parent：子模块对父模块的继承
modules：聚合多个maven项目