<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.e71</groupId>
        <artifactId>gan</artifactId>
        <version>${revision}</version>
    </parent>

    <modules>
        <module>log</module>
        <module>i18n</module>
        <module>mbp</module>
        <module>api-doc</module>
        <module>api-response</module>
        <module>api-exception</module>
        <module>cache</module>
        <module>spring-admin</module>
        <module>security</module>
        <module>test</module>
        <module>ssl</module>
    </modules>

    <artifactId>gan-kernel</artifactId>
    <packaging>pom</packaging>
    <name>kernel</name>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-exception</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-api-error</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-api-doc</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-api-response</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-test</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
