<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>cn.e71</groupId>
  <artifactId>gan</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>gan</name>
  <description>gan</description>
  <modules>
    <module>kernel</module>
    <module>business</module>
    <module>examples</module>
  </modules>
  <properties>
    <ehcache3.version>3.7.0</ehcache3.version>
    <logstash.logback.version>7.2</logstash.logback.version>
    <javafaker.version>1.0.2</javafaker.version>
    <mysql-connector.version>8.0.29</mysql-connector.version>
    <maven.javadoc.plugin.version>2.9.1</maven.javadoc.plugin.version>
    <okhttp.version>4.11.0</okhttp.version>
    <spring.boot.admin.version>2.6.2</spring.boot.admin.version>
    <junit.version>3.8.1</junit.version>
    <swagger.models.version>1.6.2</swagger.models.version>
    <caffeine.version>2.9.2</caffeine.version>
    <flyway.core.version>10.9.0</flyway.core.version>
    <mockio.version>1.10.19</mockio.version>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.plugin.version>3.12.1</maven.plugin.version>
    <spring.platform.version>Cairo-SR8</spring.platform.version>
    <revision>1.0.0-SNAPSHOT</revision>
    <spring.boot.aop.version>2.7.6</spring.boot.aop.version>
    <captcha.version>1.6.2</captcha.version>
    <spring.test.version>2.7.5</spring.test.version>
    <alibaba.seata.version>1.4.2</alibaba.seata.version>
    <knife4j.openapi3.version>4.4.0</knife4j.openapi3.version>
    <mybatis.plus.generator.version>3.5.2</mybatis.plus.generator.version>
    <spring.boot.actuator.version>2.7.6</spring.boot.actuator.version>
    <maven.surefire.plugin.version>2.18.1</maven.surefire.plugin.version>
    <minio.version>8.2.2</minio.version>
    <jcache.version>1.1.1</jcache.version>
    <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    <spring.data.version>3.2.4</spring.data.version>
    <mica.auto.version>1.2.5</mica.auto.version>
    <hutool.version>5.8.27</hutool.version>
    <spring.boot.version>3.2.4</spring.boot.version>
    <spring.retry.version>1.3.1</spring.retry.version>
    <guava.version>32.1.3-jre</guava.version>
    <sa-token.version>1.37.0</sa-token.version>
    <spring.validation.version>2.7.5</spring.validation.version>
    <ud.version>2.3.13.Final</ud.version>
    <spring.cloud.version>2021.0.1</spring.cloud.version>
    <protostuff.version>1.6.0</protostuff.version>
    <spring.plugin.version>2.0.0.RELEASE</spring.plugin.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <disruptor.version>3.4.2</disruptor.version>
    <tx.sms.version>3.1.910</tx.sms.version>
    <spring.freemarker.version>2.7.3</spring.freemarker.version>
    <spring.data.redis.version>3.0.5</spring.data.redis.version>
    <email.version>2.7.3</email.version>
    <flatten.version>1.5.0</flatten.version>
    <springfox.swagger.version>3.0.0</springfox.swagger.version>
    <java.version>17</java.version>
    <mybatis.plus.version>3.5.1</mybatis.plus.version>
    <knife4j.version>3.0.2</knife4j.version>
    <lombok.version>1.18.22</lombok.version>
    <jwt.version>0.12.5</jwt.version>
    <maven.compiler.target>17</maven.compiler.target>
    <druid.version>1.1.23</druid.version>
    <swagger-annotations.version>1.6.2</swagger-annotations.version>
    <justauth.version>1.16.4</justauth.version>
    <pagehelper.version>5.3.2</pagehelper.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>net.dreamlu</groupId>
        <artifactId>mica-auto</artifactId>
        <version>${mica.auto.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>${jwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <version>${jwt.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-spring-boot3-starter</artifactId>
        <version>${sa-token.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-jwt</artifactId>
        <version>${sa-token.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-reactor-spring-boot-starter</artifactId>
        <version>${sa-token.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-redis-jackson</artifactId>
        <version>${sa-token.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp.version}</version>
      </dependency>
      <dependency>
        <groupId>me.zhyd.oauth</groupId>
        <artifactId>JustAuth</artifactId>
        <version>${justauth.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache</artifactId>
        <version>${ehcache3.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.cache</groupId>
        <artifactId>cache-api</artifactId>
        <version>${jcache.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>caffeine</artifactId>
        <version>${caffeine.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-freemarker</artifactId>
        <version>${spring.plugin.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.whvcse</groupId>
        <artifactId>easy-captcha</artifactId>
        <version>${captcha.version}</version>
      </dependency>
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql-connector.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>${spring.data.redis.version}</version>
      </dependency>
      <dependency>
        <groupId>io.minio</groupId>
        <artifactId>minio</artifactId>
        <version>${minio.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-spring-boot-starter</artifactId>
        <version>${knife4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        <version>${knife4j.openapi3.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus</artifactId>
        <version>${mybatis.plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis.plus.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-core</artifactId>
        <version>${flyway.core.version}</version>
      </dependency>
      <dependency>
        <groupId>net.logstash.logback</groupId>
        <artifactId>logstash-logback-encoder</artifactId>
        <version>${logstash.logback.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.plugin</groupId>
        <artifactId>spring-plugin-core</artifactId>
        <version>${spring.boot.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.plugin</groupId>
        <artifactId>spring-plugin-metadata</artifactId>
        <version>${spring.boot.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring.boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-core</artifactId>
        <version>${ud.version}</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-servlet</artifactId>
        <version>2.3.13.Final</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-core</artifactId>
        <version>2.3.13.Final</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring.cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.spring.platform</groupId>
        <artifactId>platform-bom</artifactId>
        <version>${spring.platform.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper</artifactId>
        <version>${pagehelper.version}</version>
      </dependency>
      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-boot-starter</artifactId>
        <version>${springfox.swagger.version}</version>
      </dependency>
      <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-models</artifactId>
        <version>${swagger.models.version}</version>
      </dependency>
      <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-annotations</artifactId>
        <version>${swagger-annotations.version}</version>
      </dependency>
      <dependency>
        <groupId>com.tencentcloudapi</groupId>
        <artifactId>tencentcloud-sdk-java</artifactId>
        <version>${tx.sms.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mail</artifactId>
        <version>${email.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>${spring.boot.version}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-all</artifactId>
        <version>${mockio.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.javafaker</groupId>
        <artifactId>javafaker</artifactId>
        <version>${javafaker.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.e71</groupId>
        <artifactId>gan-kernel-test</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>cn.e71</groupId>
        <artifactId>gan-business-restful-starter</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>cn.e71</groupId>
        <artifactId>gan-business-ai-starter</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
  </dependencies>
  <build>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.0.1</version>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <attach>true</attach>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven.plugin.version}</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>${maven.compiler.encoding}</encoding>
          <compilerArgs>
            <arg>-parameters</arg>
          </compilerArgs>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven.javadoc.plugin.version}</version>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven.surefire.plugin.version}</version>
        <configuration>
          <skipTests>true</skipTests>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten.version}</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
