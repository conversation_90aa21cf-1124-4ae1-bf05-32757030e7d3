<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.e71</groupId>
    <artifactId>auth</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>cn.e71</groupId>
  <artifactId>gan-business-auth-sa-token</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <name>sa-token</name>
  <description>sa-token</description>
  <dependencies>
    <dependency>
      <groupId>cn.dev33</groupId>
      <artifactId>sa-token-jwt</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.dev33</groupId>
      <artifactId>sa-token-redis-jackson</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-business-auth-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>cn.dev33</groupId>
      <artifactId>sa-token-spring-boot3-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-kernel-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
