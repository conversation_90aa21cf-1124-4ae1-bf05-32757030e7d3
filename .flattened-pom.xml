<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.e71</groupId>
    <artifactId>gan-business</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>cn.e71</groupId>
  <artifactId>gan-business-restful-starter</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <name>restful-starter</name>
  <description>restful-starter</description>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-json</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-kernel-api-doc</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-kernel-api-response</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-kernel-api-exception</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-kernel-log</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-kernel-i18n</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-tomcat</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-undertow</artifactId>
    </dependency>
    <dependency>
      <groupId>io.undertow</groupId>
      <artifactId>undertow-servlet</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-kernel-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
