gan:
  kernel:
    doc:
      base-package: cn.e71
      basic-enable: true
      basic-password: 860311
      basic-username: e71-admin
      description: ${spring.application.name}-API文档
      group: 默认分组
      knife4j-enable: true
      title: ${spring.application.name}-API文档
      version: 1.0.0
knife4j:
  basic:
    enable: ${gan.kernel.doc.basic-enable}
    password: ${gan.kernel.doc.basic-password}
    username: ${gan.kernel.doc.basic-username}
  enable: ${gan.kernel.doc.knife4j-enable}
  setting:
    language: zh_cn
spring:
  application:
    name: apidoc
springdoc:
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: ${gan.kernel.doc.group}
      paths-to-match: '/**'
      packages-to-scan: ${gan.kernel.doc.base-package}
  swagger-ui:
    operations-sorter: alpha
    path: /swagger-ui.html
    tags-sorter: alpha
