package cn.e71.gex.restful.controller;

import cn.e71.gk.ar.GRestfulResult;
import cn.e71.gk.i18n.config.MicaAutoDome;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName DomeRestfulController
 * @Description: 示例代码 演示了通过gan-business-restful 快速实现 restful 接口
 * <AUTHOR>
 * @Date 2024/4/2
 * @Version V1.0
 **/
@AllArgsConstructor
@RestController
@RequestMapping("/dome")
@Slf4j
public class DomeRestfulController {

    @Autowired
    MicaAutoDome micaAutoDome;

    @GetMapping("/name")
    public ResponseEntity getName() {
        log.info("getName info {}","masterlu");
        log.debug("getName debug {}","masterlu");
        log.error("getName error {}","masterlu");
        return GRestfulResult.success("masterlu");
    }

    @GetMapping("/mica_auto")
    public ResponseEntity mica() {
        return GRestfulResult.success(micaAutoDome.sayHello());
    }

}
