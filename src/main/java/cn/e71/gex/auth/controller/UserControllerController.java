package cn.e71.gex.auth.controller;

import cn.e71.gex.auth.domain.LoginDto;
import cn.e71.gex.auth.service.UserService;
import cn.e71.gk.ar.GRestfulResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName UserControllerController
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/4/4
 * @Version V1.0
 **/

@RestController
@Tag(name = "UserControllerController", description = "UserController管理")
@RequestMapping("/user")
@AllArgsConstructor
public class UserControllerController {

    UserService userService;

    @PostMapping("/login")
    public ResponseEntity<String> doLogin(@RequestBody @Validated LoginDto loginDto) {
        return GRestfulResult.success(userService.login(loginDto.getUserName(),loginDto.getPassword()));
    }

    /***
     * 调用该接口查看登录鉴权是否生效
     * @return
     */
    @PostMapping("/info")
    public ResponseEntity<String> info() {
        return GRestfulResult.success("userinfo");
    }

}
