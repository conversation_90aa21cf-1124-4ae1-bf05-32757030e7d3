package cn.e71.gex.auth.service;

import cn.e71.gb.auth.api.GSimpleAuthApi;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @ClassName UserServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/4/4
 * @Version V1.0
 **/
@AllArgsConstructor
@Service
public class UserServiceImpl implements UserService{

    GSimpleAuthApi gSimpleAuthApi;

    @Override
    public String login(String userName, String password) {
        return gSimpleAuthApi.simpleJwtToken(userName);
    }
}
