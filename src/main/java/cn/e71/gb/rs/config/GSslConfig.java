package cn.e71.gb.rs.config;


import cn.hutool.core.collection.ListUtil;
import io.undertow.Undertow;
import io.undertow.UndertowOptions;
import io.undertow.servlet.api.SecurityConstraint;
import io.undertow.servlet.api.SecurityInfo;
import io.undertow.servlet.api.TransportGuaranteeType;
import io.undertow.servlet.api.WebResourceCollection;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.context.annotation.Bean;

import java.util.List;

/**
 * @ClassName SslConfig
 * @Description: ssl 配置
 * <AUTHOR>
 * @Date 2024/5/8
 * @Version V1.0
 **/
@AutoConfiguration
@EnableConfigurationProperties(GSslProperties.class)
@ConditionalOnProperty(name = "server.ssl.enabled", havingValue = "true")
public class GSslConfig {

    public static final String ANY_HOST = "0.0.0.0";
    public static final String ANY_HOST_IPV6 = "::0";

    public static final int DEFAULT_HTTP_PORT = 8080;

    public static final int DEFAULT_HTTPS_PORT = 8443;

    @Value("${server.ssl.ignoreUrls}")
    private List<String> ignoreUrls = ListUtil.empty();
    @Value("${server.httpPort}")
    private int httpPort = DEFAULT_HTTP_PORT;
    @Value("${server.port}")
    private int httpsPort = DEFAULT_HTTPS_PORT;

    @Bean
    public ServletWebServerFactory servletWebServerFactory() {
        UndertowServletWebServerFactory undertowFactory = new UndertowServletWebServerFactory();
        setupHttpPortListener(undertowFactory);
        setupPortExchange(undertowFactory);
        setupSkipSslUrl(undertowFactory);
        return undertowFactory;
    }


    private void setupHttpPortListener(UndertowServletWebServerFactory undertowServletWebServerFactory){
        undertowServletWebServerFactory.addBuilderCustomizers((Undertow.Builder builder) -> {
            builder.addHttpListener(httpPort, ANY_HOST);
            builder.addHttpListener(httpPort, ANY_HOST_IPV6);
            builder.setServerOption(UndertowOptions.ENABLE_HTTP2, true);
        });
    }

    private void setupPortExchange(UndertowServletWebServerFactory undertowServletWebServerFactory){
        undertowServletWebServerFactory.addDeploymentInfoCustomizers(deploymentInfo ->
                deploymentInfo.addSecurityConstraint(
                        new SecurityConstraint()
                                .addWebResourceCollection(new WebResourceCollection().addUrlPattern("/*"))
                                .setTransportGuaranteeType(TransportGuaranteeType.CONFIDENTIAL)
                                .setEmptyRoleSemantic(SecurityInfo.EmptyRoleSemantic.PERMIT)
                ).setConfidentialPortManager(exchange -> httpsPort)
        );
    }

    private void setupSkipSslUrl(UndertowServletWebServerFactory undertowServletWebServerFactory){

        undertowServletWebServerFactory.addDeploymentInfoCustomizers(deploymentInfo ->
                deploymentInfo.addSecurityConstraint(
                        new SecurityConstraint()
                                .addWebResourceCollection(new WebResourceCollection()
                                        .addUrlPatterns(
                                                ignoreUrls
                                        ))
                                .setTransportGuaranteeType(TransportGuaranteeType.NONE)
                                .setEmptyRoleSemantic(SecurityInfo.EmptyRoleSemantic.PERMIT)
                )
        );
    }

}
