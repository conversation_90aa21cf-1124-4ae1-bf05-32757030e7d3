package cn.e71.gb.rs.exception;

import cn.e71.gk.ae.GErrorEntity;
import cn.e71.gk.ae.GException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * @ClassName GlobalExceptionHandler
 * @Description: 全局异常处理
 * <AUTHOR>
 * @Date 2022/3/28
 * @Version V1.0
 **/
@ControllerAdvice
@AllArgsConstructor
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(value = GException.class)
    public ResponseEntity<GErrorEntity> handleException(GException e) {
        return e.toResponseEntity();
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Object handleException(MethodArgumentNotValidException e) {
        return null;
    }

    @ResponseBody
    @ExceptionHandler(value = BindException.class)
    public Object handleException(BindException e) {
        return null;
    }

}
