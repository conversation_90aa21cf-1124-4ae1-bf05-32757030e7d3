package cn.e71.gb.auth.starter;

import cn.e71.gb.auth.api.GSimpleAuthApi;
import cn.e71.gb.auth.st.GAuthApiSaTokenImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

/**
 * @ClassName GAuthAutoConfigure
 * @Description: auth starter 自动装配
 * <AUTHOR>
 * @Date 2024/4/4
 * @Version V1.0
 **/
@AutoConfiguration
public class GAuthAutoConfigure {

    @Bean
    @ConditionalOnMissingBean
    public GSimpleAuthApi gSimpleAuthApi(){
        return new GAuthApiSaTokenImpl();
    }

}
