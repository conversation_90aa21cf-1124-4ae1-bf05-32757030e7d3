package cn.e71.gb.auth.api.exception;

import cn.e71.gb.auth.api.AuthConstants;
import cn.e71.gk.ae.GErrorEntity;

/**
 * @ClassName AuthModuleErrors
 * @Description: 认证模块错误枚举
 * <AUTHOR>
 * @Date 2024/4/5
 * @Version V1.0
 **/
public enum AuthModuleErrors implements GErrorEntity {


    CHECK_LOGIN_ERROR(AuthConstants.MODULE_CODE + "001", "登录鉴权失败"),
    USER_NAME_EMPTY(AuthConstants.MODULE_CODE + "002", "获取token失败,用户名不能为空"),
    SATOKEN_LOGIN_ERROR(AuthConstants.MODULE_CODE + "003", "获取token失败,认证框架异常");

    private String code;

    private String message;


    AuthModuleErrors(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
