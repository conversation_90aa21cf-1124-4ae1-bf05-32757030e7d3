package cn.e71.gb.auth.api.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @ClassName GAuthProperties
 * @Description: 认证配置属性类
 * <AUTHOR>
 * @Date 2024/4/4
 * @Version V1.0
 **/
@Data
@ConfigurationProperties("gan.business.auth")
public class GAuthProperties {

    LoginRule checkLoginRule = new LoginRule();

    @Data
    public static final class LoginRule{

        public static final String DEFAULT_PATTERN = "/**";
        public static final String [] DEFAULT_EXCLUDE = new String []{
                "/swagger-ui/",
                "/swagger-resources/**",
                "/swagger-ui/**",
                "/v3/api-docs",
                "/v2/api-docs",
                "/v1/api-docs",
                "/swagger-ui.html",
                "/doc.html",
                "/webjars/**",
                "/**/v3/api-docs",
                "/**/v2/api-docs",
                "/**/*.html",
                "/**/*.js",
                "/favicon.ico",
                "/**/*.css",
                "/actuator/**",
                "/druid/**",
                "/**/login",
                "/**/register",
                "/captcha"
        };

        private String [] patternList = new String[]{DEFAULT_PATTERN};
        private String [] excludeList = DEFAULT_EXCLUDE;
    }
}
