package cn.e71.gb.auth.st;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import cn.e71.gb.auth.api.exception.AuthModuleErrors;
import cn.e71.gb.auth.api.properties.GAuthProperties;
import cn.e71.gk.ae.GAssert;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpStatus;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @ClassName SaTokenConfig
 * @Description: sa-token 配置类
 * <AUTHOR>
 * @Date 2024/4/4
 * @Version V1.0
 **/
@Configuration
@EnableConfigurationProperties(GAuthProperties.class)
@AllArgsConstructor
@Slf4j
public class SaTokenConfigure implements WebMvcConfigurer {

    GAuthProperties properties;

    /***
     * token 名称（同时也是 cookie 名称）
     */
    public static final String SA_TOKEN_DEFAULT_TOKEN_NAME = "gan-sa-token-jwt";

    /**
     * token 有效期（单位：秒），默认30天，-1代表永不过期
     */
    public static final long SA_TOKEN_DEFAULT_TIMEOUT = 30 * 24 * 60 * 60;
    /***
     * token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
     */
    public static final long SA_TOKEN_DEFAULT_ACTIVE_TIMEOUT = -1;
    /***
     * JwtSecretKey
     */
    public static final String SA_TOKEN_DEFAULT_JWT_SECRET_KEY = "e71jwtSSk_!-fck30,c";


    @Bean
    @Primary
    public SaTokenConfig getSaTokenConfigPrimary() {
        SaTokenConfig config = new SaTokenConfig();
        config.setTokenName(SA_TOKEN_DEFAULT_TOKEN_NAME);
        config.setTimeout(SA_TOKEN_DEFAULT_TIMEOUT);
        config.setActiveTimeout(SA_TOKEN_DEFAULT_ACTIVE_TIMEOUT);
        config.setIsConcurrent(true);
        config.setIsShare(true);
        config.setIsLog(false);
        config.setJwtSecretKey(SA_TOKEN_DEFAULT_JWT_SECRET_KEY);
        return config;
    }

    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SaInterceptor(handle -> checkLogin(handle)))
                .addPathPatterns(properties.getCheckLoginRule().getPatternList())
                .excludePathPatterns(properties.getCheckLoginRule().getExcludeList());
    }

    private void checkLogin(Object o) {
        try{
            StpUtil.checkLogin();
        }catch (NotLoginException e){
            log.info("=== {} 认证时发生异常 ===",o);
            GAssert.fail(HttpStatus.UNAUTHORIZED,AuthModuleErrors.CHECK_LOGIN_ERROR);
        }
    }
}
