package cn.e71.gb.auth.st;


import cn.dev33.satoken.stp.SaLoginConfig;
import cn.dev33.satoken.stp.StpUtil;
import cn.e71.gb.auth.api.GSimpleAuthApi;
import cn.e71.gb.auth.api.exception.AuthModuleErrors;
import cn.e71.gk.ae.GAssert;
import cn.hutool.core.util.StrUtil;
import org.springframework.http.HttpStatus;

/**
 * @ClassName GAuthApiSaTokenImpl
 * @Description: 使用satoken 实现 GAuthApi
 * <AUTHOR>
 * @Date 2024/4/4
 * @Version V1.0
 **/
public class GAuthApiSaTokenImpl implements GSimpleAuthApi {

    @Override
    public String simpleJwtToken(String username) {
        if(StrUtil.isEmpty(username)){
            GAssert.fail(HttpStatus.BAD_REQUEST, AuthModuleErrors.USER_NAME_EMPTY);
        }
        try{
            StpUtil.login(username, SaLoginConfig.setExtra("username", username));
        }catch (Exception e){
            GAssert.fail(HttpStatus.INTERNAL_SERVER_ERROR, AuthModuleErrors.SATOKEN_LOGIN_ERROR);
        }
        return StpUtil.getTokenInfo().tokenValue;
    }

}
