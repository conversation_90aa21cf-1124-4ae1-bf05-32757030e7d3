package cn.e71.gb.tpauth.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName ThirdPartyAuthInfoDto
 * @Description: 三方授权认证信息实体(这里参考justAuth 的实现尽可能的全部列出所有平台的参数)
 * <AUTHOR>
 * @Date 2024/4/5
 * @Version V1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ThirdPartyAuthInfoDto {

    // 以下参数为参考，具体以实际使用平台为准
    // 参考：https://gitee.com/yadong.zhang/JustAuth/wikis/%E5%85%AC%E5%BC%80%E6%96%87%E6%A1%A3?sort_id=3148694
    String id;
    String uuid;
    String source;
    String openId;
    Long expireIn;
    String accessToken;
    String refreshToken;
    String userid;
    String accessCode;
    String scope;
    String unionId;
    String tokenType;
    String idToken;
    String macAlgorithm;
    String macKey;
    String code;
    String oauthToken;
    String oauthTokenSecret;

}
