package cn.e71.gb.tpauth;

import cn.e71.gk.ae.GErrorEntity;

/**
 * @ClassName ThirdPartAuthConstants
 * @Description: 模块常量
 * <AUTHOR>
 * @Date 2024/4/5
 * @Version V1.0
 **/
public class ThirdPartAuthConstants {

    private static final String MODULE_CODE = "02";


    public enum ThirdPartyAuthErrors implements GErrorEntity{
        SOURCE_NOT_FOUND(MODULE_CODE+"001","第三方登录源未正确配置");

        ThirdPartyAuthErrors(String code, String message) {
            this.code = code;
            this.message = message;
        }

        String code;
        String message;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getMessage() {
            return message;
        }
    }


}
