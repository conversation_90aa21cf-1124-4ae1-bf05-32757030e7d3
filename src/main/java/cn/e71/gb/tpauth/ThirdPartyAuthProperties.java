package cn.e71.gb.tpauth;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ThirdPartyAuthProperties
 * @Description: 第三方登录配置属性类
 * <AUTHOR>
 * @Date 2024/4/5
 * @Version V1.0
 **/
@ConfigurationProperties("gan.business.tpauth")
@Data
public class ThirdPartyAuthProperties {

    private List<ThirdPartyAuthSource> sourceList = new ArrayList<>();

    @Data
    public static final class ThirdPartyAuthSource{
        private String source;
        private String clientId;
        private String clientSecret;
        private String redirectUri;
    }


}
