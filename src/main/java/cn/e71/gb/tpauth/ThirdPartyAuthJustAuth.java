package cn.e71.gb.tpauth;

import cn.e71.gk.ae.GAssert;
import lombok.AllArgsConstructor;
import me.zhyd.oauth.request.AuthRequest;
import org.springframework.stereotype.Component;
import java.util.Map;

import static cn.e71.gb.tpauth.ThirdPartAuthConstants.ThirdPartyAuthErrors.SOURCE_NOT_FOUND;

/**
 * @ClassName ThirdPartyAuthJustAuth
 * @Description: 三方登录JustAuth实现
 * <AUTHOR>
 * @Date 2024/4/5
 * @Version V1.0
 **/

@Component
@AllArgsConstructor
public class ThirdPartyAuthJustAuth implements ThirdPartyAuthApi{
    Map<String, AuthRequest> requestMap;
    @Override
    public void requestAuth(String source) {
        var request = requestMap.get(source);
        if(request == null){
            GAssert.failInternalServerError(SOURCE_NOT_FOUND);
        }
        request.authorize("state");
    }
}
