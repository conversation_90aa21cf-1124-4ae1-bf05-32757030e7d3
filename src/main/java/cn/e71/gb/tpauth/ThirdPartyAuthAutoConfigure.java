package cn.e71.gb.tpauth;

import me.zhyd.oauth.AuthRequestBuilder;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.request.AuthRequest;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ThridPartyAuthAutoConfigure
 * @Description: 第三方认证自动配置类
 * <AUTHOR>
 * @Date 2024/4/5
 * @Version V1.0
 **/
@Component
@EnableConfigurationProperties(ThirdPartyAuthProperties.class)
public class ThirdPartyAuthAutoConfigure {

    @Bean
    public Map<String, AuthRequest> authRequestMap(ThirdPartyAuthProperties thirdPartyAuthProperties) {
        Map<String, AuthRequest> authRequestMap = new HashMap<>();
        thirdPartyAuthProperties.getSourceList().forEach(source ->
                authRequestMap.put(source.getSource(), initAuthRequest(source)));
        return authRequestMap;
    }


    private AuthRequest initAuthRequest(ThirdPartyAuthProperties.ThirdPartyAuthSource source) {
        return AuthRequestBuilder.builder()
                .source(source.getSource())
                .authConfig(AuthConfig.builder()
                        .clientId(source.getClientId())
                        .clientSecret(source.getClientSecret())
                        .redirectUri(source.getRedirectUri())
                        .build())
                .build();
    }


}
