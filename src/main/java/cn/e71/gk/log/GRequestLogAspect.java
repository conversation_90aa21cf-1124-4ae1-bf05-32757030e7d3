package cn.e71.gk.log;
import cn.e71.gk.log.pojo.GRequestLog;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


@Aspect
@Order(1)
@Component
@Slf4j
public class GRequestLogAspect {

    @Value("${spring.application.name}")
    private String appName;
    @Value("${spring.profiles.active}")
    private String appEnv;

    @Pointcut("execution(public * cn.e71.*.controller.*.*(..))||execution(public * cn.e71.*.*.controller.*.*(..))")
    void requestLog() {}

    @Around("requestLog()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Object response = joinPoint.proceed();
        GRequestLog requestLog = GRequestLog.from(appName,appEnv,joinPoint, getServletRequest(),response);
        log.info(requestLog.getMarkers(), JSONUtil.parse(requestLog).toString());
        return response;
    }

    private HttpServletRequest getServletRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }


}
