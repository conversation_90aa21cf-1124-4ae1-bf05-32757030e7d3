package cn.e71.gk.log.pojo;


import cn.hutool.core.lang.UUID;
import lombok.Data;
import java.util.Date;

@Data
public class GLog {
    /***
     * 日志Id
     */
    private String id = UUID.fastUUID().toString(true);
    /***
     * 日志生成时间
     */
    private Date createAt = new Date();
    /***
     * 日志产生的服务名
     */
    private String appName = "Gan-Service";
    /***
     * 日志产生的服务id(集群环境下有值)
     */
    private String appNumber = "Gan-Service-1";
    /***
     * 日志产生的服务环境
     */
    private String appEnv = "dev";

}
