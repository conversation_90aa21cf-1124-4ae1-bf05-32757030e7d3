package cn.e71.gk.log.pojo;

import cn.e71.gk.log.utils.GLogUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import net.logstash.logback.marker.Markers;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Marker;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;


@Data
public class GRequestLog extends GLog{

    /**
     * 请求用户
     */
    private String username;
    

    /**
     * 请求时间
     */
    private Long startTime;

    /**
     * 消耗时间
     */
    private Long spendTime;

    /**
     * 根路径
     */
    private String basePath;

    /**
     * URI
     */
    private String uri;

    /**
     * 请求方法
     */
    private String method;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 请求参数
     */
    private Object parameter;

    /***
     * 返回结果
     */
    private Object result;



    public static GRequestLog from(String appName, String appEnv, ProceedingJoinPoint joinPoint, HttpServletRequest request,Object result) throws Throwable {

        GRequestLog requestLog = new GRequestLog();
        requestLog.setAppName(appName);
        requestLog.setAppEnv(appEnv);
        requestLog.setAppNumber(appName + "-" + request.getLocalAddr());

        long startTime = System.currentTimeMillis();

        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();


        String urlStr = request.getRequestURL().toString();
        requestLog.setBasePath(StrUtil.removeSuffix(urlStr, URLUtil.url(urlStr).getPath()));
        requestLog.setUsername(request.getRemoteUser());
        requestLog.setIp(GLogUtil.getRequestIp(request));
        requestLog.setMethod(request.getMethod());
        requestLog.setParameter(GLogUtil.getParameter(method, joinPoint.getArgs()));
        requestLog.setSpendTime(System.currentTimeMillis() - startTime);
        requestLog.setStartTime(startTime);
        requestLog.setUri(request.getRequestURI());

        requestLog.setResult(result);

        return requestLog;
    }


    public Marker getMarkers() {
        Map<String,Object> logMap = new HashMap<>();
        logMap.put("uri",getUri());
        logMap.put("method",getMethod());
        logMap.put("parameter",getParameter());
        logMap.put("spendTime",getSpendTime());
        return Markers.appendEntries(logMap);
    }



}
