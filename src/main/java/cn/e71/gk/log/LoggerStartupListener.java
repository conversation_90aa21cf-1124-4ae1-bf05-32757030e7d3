package cn.e71.gk.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.LoggerContextListener;
import ch.qos.logback.core.spi.ContextAwareBase;
import ch.qos.logback.core.spi.LifeCycle;

/**
 * @ClassName LoggerStartupListener
 * @Description: 重新设置日志配置
 * <AUTHOR>
 * @Date 2024/3/29
 * @Version V1.0
 **/


public class LoggerStartupListener extends ContextAwareBase implements LoggerContextListener, LifeCycle {

    private boolean started = false;

    @Override
    public void start() {
        if(started) {
            return;
        }
        System.out.println("loggerStartupListener start");
        LoggerContext context = (LoggerContext) getContext();
//        context.putProperty("MY_HOME", userHome);
//        context.putProperty("LOG_FILE", logFile);
        started = true;
    }

    @Override
    public void stop() {

    }

    @Override
    public boolean isStarted() {
        return false;
    }

    @Override
    public boolean isResetResistant() {
        return false;
    }

    @Override
    public void onStart(LoggerContext loggerContext) {

    }

    @Override
    public void onReset(LoggerContext loggerContext) {

    }

    @Override
    public void onStop(LoggerContext loggerContext) {

    }

    @Override
    public void onLevelChange(Logger logger, Level level) {

    }
}
