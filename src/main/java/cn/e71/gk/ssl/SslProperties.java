package cn.e71.gk.ssl;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @ClassName SslProperties
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/5/8
 * @Version V1.0
 **/
@Data
@ConfigurationProperties(prefix = "gan.kernel.ssl")
public class SslProperties {
    private String KeyStoreType;
    private String KeyStorePath;
    private String KeyStorePassword;
    private String TrustStoreType;
    private String TrustStorePath;
    private String TrustStorePassword;
    private int httpPort;
    private int httpsPort;
}
