package cn.e71.gk.apidoc.config;

import cn.e71.gk.apidoc.pojo.GOpenApiProperties;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import lombok.Data;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName OpenApiConfig
 * @Description: Open api 配置
 * <AUTHOR>
 * @Date 2022/4/1
 * @Version V1.0
 **/

@Configuration
@EnableConfigurationProperties(GOpenApiProperties.class)
@Data
public class OpenApiConfig {


    @Bean
    public OpenAPI customOpenAPI(GOpenApiProperties openApiProperties) {
        return new OpenAPI()
                .info(new Info()
                        .title(openApiProperties.getTitle())
                        .version(openApiProperties.getVersion())
                        .description(openApiProperties.getDescription()));
    }

}
