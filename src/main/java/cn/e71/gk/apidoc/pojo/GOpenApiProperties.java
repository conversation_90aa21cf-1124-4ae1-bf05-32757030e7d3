package cn.e71.gk.apidoc.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;


/**
 * @ClassName GOpenApiProperties
 * @Description: OpenApi配置属性
 * <AUTHOR>
 * @Date 2024/4/1
 * @Version V1.0
 **/
@Data
@AllArgsConstructor
@ConfigurationProperties(prefix = "gan.kernel.doc")
public class GOpenApiProperties {

    private String title;
    private String description;
    private String version;

}
