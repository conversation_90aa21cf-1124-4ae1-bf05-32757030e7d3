package cn.e71.gk.ae;


/**
 * @ClassName GanErrorEntityEnum
 * @Description: 错误实体枚举
 * <AUTHOR>
 * @Date 2024/3/29
 * @Version V1.0
 **/
public enum GErrorEntityEnum implements GErrorEntity{

    UNKNOWN("00000","未知错误");

    String code;
    String message;


    GErrorEntityEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
