package cn.e71.gk.ae;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;



/**
 * @ClassName GanException
 * @Description: 通用异常类，所有模块发生异常处理都应该使用之
 * <AUTHOR>
 * @Date 2021/3/27
 * @Version V1.0
 **/
@Data
@AllArgsConstructor
public class GException extends RuntimeException implements GErrorEntity{

    /***
     * 异常状态码
     */
    private HttpStatus status;

    /***
     * 错误码
     */
    private String code;

    /***
     * 错误消息
     */
    private String message;

    /***
     * 错误数据
     */
    private Object errorData;


    public GException(HttpStatus status,GErrorEntity errorEntity, Object errorData) {
        super(errorEntity.getMessage());
        this.status = status;
        this.code = errorEntity.getCode();
        this.message = errorEntity.getMessage();
        this.errorData = errorData;
    }

    public GException(HttpStatus status,GErrorEntity errorEntity) {
        this(status,errorEntity,null);
    }

    public ResponseEntity<GErrorEntity> toResponseEntity() {
        return new ResponseEntity<>(this, status);
    }

}
