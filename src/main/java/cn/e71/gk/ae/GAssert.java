package cn.e71.gk.ae;

import org.springframework.http.HttpStatus;
import java.util.Collection;

/**
 * @ClassName GAssert
 * @Description: 快速断言辅助类
 * <AUTHOR>
 * @Date 2020/3/29
 * @Version V1.0
 **/
public class GAssert {

    public static void fail(HttpStatus status, GErrorEntity error){
        throw new GException(status,error);
    }

    public static void failInternalServerError(GErrorEntity error){
        throw new GException(HttpStatus.INTERNAL_SERVER_ERROR,error);
    }

    public static void failBadRequest(GErrorEntity error){
        throw new GException(HttpStatus.BAD_REQUEST,error);
    }

    public static void fail(GException e){
        throw e;
    }

    public static void isFalse(boolean expression,HttpStatus status, GErrorEntity error){
        if(!expression){
            fail(status,error);
        }
    }

    public static void isTrue(boolean expression,HttpStatus status, GErrorEntity error){
        if(expression){
            fail(status,error);
        }
    }

    public static void isNull(Object object,HttpStatus status, GErrorEntity error){
        if(null == object){
            fail(status,error);
        }
    }

    public static void isEmpty(Collection collection,HttpStatus status, GErrorEntity error){
        if(null == collection || collection.isEmpty()){
            fail(status,error);
        }
    }



}
