package cn.e71.gk.ar;


import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * @ClassName GPage
 * @Description: 分页数据
 * <AUTHOR>
 * @Date 2021/3/28
 * @Version V1.0
 **/
@Data
@Builder
public class GPage<T> {

    /***
     * 页码
     */
    private Long pageNumber;
    /***
     * 页容量
     */
    private Long pageSize;
    /***
     * 总页数
     */
    private Long totalPage;
    /***
     * 总记录数
     */
    private Long total;
    /***
     * 数据集合
     */
    private List<T> dataList;
}
