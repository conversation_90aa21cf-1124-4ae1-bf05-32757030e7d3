package cn.e71.gk.ar;

import org.springframework.http.ResponseEntity;

import java.util.Optional;

/**
 * @ClassName RestfulResult
 * @Description: Restful 风格返回实体
 * <AUTHOR>
 * @Date 2024/4/1
 * @Version V1.0
 **/
public class GRestfulResult {
    /***
     * 对应200 status返回 直接从body取到实体数据
     * @param data 实体数据
     * @return ResponseEntity 包裹返回实体，该返回实体会自动将状态码设置为200
     */
    public static <T> ResponseEntity<T> success(T data){
        return ResponseEntity.of(Optional.ofNullable(data));
    }
}
