package cn.e71.gk.ar;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @ClassName GResult
 * @Description: 响应结果实体
 * <AUTHOR>
 * @Date 2024/3/29
 * @Version V1.0
 **/
@Data
@AllArgsConstructor
public class GResult<T> {

    public static final String SUCCESS_ERROR_CODE = "0";
    public static final String SUCCESS_ERROR_MESSAGE = "";
    public static final int SUCCESS_STATUS_CODE = 200;

    public  static  <T>GResult success(T data){
        return new GResult<>(SUCCESS_STATUS_CODE,SUCCESS_ERROR_CODE,SUCCESS_ERROR_MESSAGE,data);
    }

    /***
     * 响应的状态码，对应httpStatus
     */
    private int statusCode;
    /***
     * 错误代码，成功时为0
     */
    private String errorCode;
    /***
     * 错误消息，成功时为ok
     */
    private String errorMessage;
    /***
     * 响应实体对象
     */
    private T data;
}
