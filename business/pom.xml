<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.e71</groupId>
        <artifactId>gan</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>gan-business</artifactId>
    <name>business</name>
    <packaging>pom</packaging>
    <modules>
        <module>restful</module>
        <module>auth</module>
        <module>third-party-auth</module>
        <module>ai</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-api-doc</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--集成api基础response实体 -->
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-api-response</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--集成api基础异常 -->
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-api-exception</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--集成日志 -->
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-log</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--集成i18n -->
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-i18n</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
