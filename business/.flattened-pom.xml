<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.e71</groupId>
    <artifactId>gan</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>cn.e71</groupId>
  <artifactId>gan-business</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>business</name>
  <modules>
    <module>restful</module>
    <module>auth</module>
    <module>third-party-auth</module>
    <module>ai</module>
  </modules>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-spring-boot-starter</artifactId>
        <version>${sa-token.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.e71</groupId>
        <artifactId>gan-kernel-api-doc</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.e71</groupId>
        <artifactId>gan-kernel-api-response</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.e71</groupId>
        <artifactId>gan-kernel-api-exception</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.e71</groupId>
        <artifactId>gan-kernel-log</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.e71</groupId>
        <artifactId>gan-kernel-i18n</artifactId>
        <version>${revision}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
