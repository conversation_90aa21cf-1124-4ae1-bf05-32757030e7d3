<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.e71</groupId>
        <artifactId>ai</artifactId>
        <version>${revision}</version>
    </parent>
    <groupId>cn.e71</groupId>
    <artifactId>gan-business-ai-core</artifactId>
    <version>${revision}</version>
    <name>gan-business-ai-core</name>
    <description>ai core</description>
    <packaging>jar</packaging>
    <dependencies>
        <!--集成日志 -->
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-log</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!-- 移除掉默认支持的 Tomcat -->
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.genai</groupId>
            <artifactId>google-genai</artifactId>
            <version>1.13.0</version>
        </dependency>
        <!-- 测试依赖 -->
        <dependency>
            <groupId>cn.e71</groupId>
            <artifactId>gan-kernel-test</artifactId>
            <version>${revision}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>