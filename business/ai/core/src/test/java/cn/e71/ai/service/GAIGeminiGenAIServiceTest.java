package cn.e71.ai.service;

import cn.e71.ai.config.GAIProperties;
import cn.e71.ai.domain.dto.AIStreamData;
import com.google.genai.Client;
import com.google.genai.Models;
import com.google.genai.ResponseStream;
import com.google.genai.types.GenerateContentResponse;
import com.google.genai.types.FinishReason;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.Iterator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class GAIGeminiGenAIServiceTest {

    @Mock
    private GAIProperties gaiProperties;

    @Mock
    private Client client;

    @Mock
    private Models models;

    @Mock
    private GenerateContentResponse generateContentResponse;

    @Mock
    private ResponseStream<GenerateContentResponse> responseStream;

    private GAIGeminiGenAIService gaiService;

    @BeforeEach
    void setUp() {
        // 设置模拟的配置
        when(gaiProperties.getApiKey()).thenReturn("test-api-key");
        when(gaiProperties.getApiModel()).thenReturn("gemini-2.5-flash");
        
        // 创建服务实例
        gaiService = new GAIGeminiGenAIService(gaiProperties);
        
        // 使用反射设置模拟的 client
        try {
            java.lang.reflect.Field clientField = GAIGeminiGenAIService.class.getDeclaredField("client");
            clientField.setAccessible(true);
            clientField.set(gaiService, client);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set mock client", e);
        }
    }

    @Test
    void testChat_Success() {
        // 准备测试数据
        String inputText = "Hello, how are you?";
        String expectedResponse = "I'm doing well, thank you!";
        
        // 设置模拟行为
        when(client.models).thenReturn(models);
        when(models.generateContent(anyString(), anyString(), null)).thenReturn(generateContentResponse);
        when(generateContentResponse.text()).thenReturn(expectedResponse);
        
        // 执行测试
        String result = gaiService.chat(inputText);
        
        // 验证结果
        assertEquals(expectedResponse, result);
        
        // 验证方法调用
        verify(models).generateContent("gemini-2.5-flash", inputText, null);
        verify(generateContentResponse).text();
    }

    @Test
    void testChatForStream_Success() {
        // 准备测试数据
        String inputText = "Tell me a story";
        String expectedText1 = "Once upon a time";
        String expectedText2 = " there was a brave knight";
        
        // 创建模拟的响应流
        GenerateContentResponse response1 = mock(GenerateContentResponse.class);
        GenerateContentResponse response2 = mock(GenerateContentResponse.class);
        
        when(response1.text()).thenReturn(expectedText1);
        when(response1.finishReason().knownEnum()).thenReturn(FinishReason.Known.FINISH_REASON_UNSPECIFIED);
        when(response2.text()).thenReturn(expectedText2);
        when(response2.finishReason().knownEnum()).thenReturn(FinishReason.Known.STOP);
        
        Iterator<GenerateContentResponse> iterator = Arrays.asList(response1, response2).iterator();
        
        // 设置模拟行为
        when(client.models).thenReturn(models);
        when(models.generateContentStream(anyString(), anyString(), null)).thenReturn(responseStream);
        when(responseStream.iterator()).thenReturn(iterator);
        
        // 执行测试
        Flux<AIStreamData> result = gaiService.chatForStream(inputText);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(data -> 
                    expectedText1.equals(data.getText()) && 
                    "stop".equals(data.getFinishReason()))
                .expectNextMatches(data -> 
                    expectedText2.equals(data.getText()) && 
                    "stop".equals(data.getFinishReason()))
                .verifyComplete();
        
        // 验证方法调用
        verify(models).generateContentStream("gemini-2.5-flash", inputText, null);
    }

    @Test
    void testChatForStream_WithNullFinishReason() {
        // 准备测试数据
        String inputText = "Test message";
        String expectedText = "Test response";
        
        // 创建模拟的响应
        GenerateContentResponse response = mock(GenerateContentResponse.class);
        when(response.text()).thenReturn(expectedText);
        when(response.finishReason()).thenReturn(null);
        
        Iterator<GenerateContentResponse> iterator = Arrays.asList(response).iterator();
        
        // 设置模拟行为
        when(client.models).thenReturn(models);
        when(models.generateContentStream(anyString(), anyString(), null)).thenReturn(responseStream);
        when(responseStream.iterator()).thenReturn(iterator);
        
        // 执行测试
        Flux<AIStreamData> result = gaiService.chatForStream(inputText);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(data -> 
                    expectedText.equals(data.getText()) && 
                    "stop".equals(data.getFinishReason()))
                .verifyComplete();
    }

    @Test
    void testChatForStream_EmptyResponse() {
        // 准备测试数据
        String inputText = "Empty response test";
        
        // 创建空的响应流
        Iterator<GenerateContentResponse> emptyIterator = Arrays.<GenerateContentResponse>asList().iterator();
        
        // 设置模拟行为
        when(client.models).thenReturn(models);
        when(models.generateContentStream(anyString(), anyString(), null)).thenReturn(responseStream);
        when(responseStream.iterator()).thenReturn(emptyIterator);
        
        // 执行测试
        Flux<AIStreamData> result = gaiService.chatForStream(inputText);
        
        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void testConstructor_InitializesClient() {
        // 测试构造函数是否正确初始化了客户端
        when(gaiProperties.getApiKey()).thenReturn("test-key");
        
        // 创建新的服务实例
        GAIGeminiGenAIService newService = new GAIGeminiGenAIService(gaiProperties);
        
        // 验证配置被正确使用
        verify(gaiProperties).getApiKey();
    }
}
