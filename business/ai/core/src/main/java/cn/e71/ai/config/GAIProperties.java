package cn.e71.ai.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

@ConfigurationProperties(prefix = "gan.ai")
@Configuration
@Data
public class GAIProperties {

    /**
     * api平台，默认：openai
     */
    private GAIPlatfrom apiPlatform = GAIPlatfrom.GEMINI;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * API密钥池,用于轮询使用，突破单个API密钥的限制
     */
    private List<String> apiKeyPool;

    /**
     * API密钥的密文
     */
    private String apiSecret;

    /**
     * API请求地址
     */
    private String apiUrl;

    /**
     * 使用的模型，默认：gpt-4o-min
     */
    private String apiModel = "gemini-2.5-flash";

    /**
     * API版本，默认：2024-02-15
     */
    private String apiVersion = "2024-02-15";

    /**
     * 请求格式，默认：json
     */
    private String apiFormat = "json";

    /**
     * 请求超时时间（毫秒），默认：30000
     */
    private String apiTimeout = "30000";

    /**
     * 最大tokens数，默认：4096
     */
    private String apiMaxTokens = "4096";

    /**
     * 温度参数，默认：0.5
     */
    private String apiTemperature = "0.5";

    /**
     * top_p参数，默认：1
     */
    private String apiTopP = "1";

    /**
     * 频率惩罚参数，默认：0
     */
    private String apiFrequencyPenalty = "0";

    /**
     * 存在惩罚参数，默认：0
     */
    private String apiPresencePenalty = "0";

    /**
     * 停止词，默认：空字符串
     */
    private String apiStop = "";

    /**
     * 响应格式，默认：json
     */
    private String apiResponseFormat = "json";

    /**
     * 响应格式类型，默认：json
     */
    private String apiResponseFormatType = "json";
}
