package cn.e71.ai.service;


import org.springframework.stereotype.Service;
import com.google.genai.Client;
import com.google.genai.ResponseStream;
import com.google.genai.types.ClientOptions;
import com.google.genai.types.GenerateContentResponse;
import cn.e71.ai.config.GAIProperties;
import cn.e71.ai.domain.dto.AIStreamData;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
@Service
public class GAIGeminiGenAIService implements GAIService {

    private GAIProperties gaiProperties;
    private Client client;

    public GAIGeminiGenAIService(GAIProperties gaiProperties) {
        this.gaiProperties = gaiProperties;
        initClient();
    }

    private void initClient() {
        log.info("GAIGeminiGenAIService initClient: {}", gaiProperties);
        client = Client.builder().apiKey(gaiProperties.getApiKey())
                .clientOptions(ClientOptions.builder().maxConnections(64).maxConnectionsPerHost(16).build()).build();
    }

    @Override
    public String chat(String text) {
        log.info("GAIGeminiGenAIService chat: {}", text);
        GenerateContentResponse response = client.models.generateContent(this.gaiProperties.getApiModel(), text, null);
        return response.text();
    }

    @Override
    public Flux<AIStreamData> chatForStream(String text) {
        log.info("GAIGeminiGenAIService chatForStream: {}", text);
        ResponseStream<GenerateContentResponse> response = client.models.generateContentStream(this.gaiProperties.getApiModel(), text, null);
        Flux<AIStreamData> fluxResult = Flux.fromIterable(response).map(item -> {
            AIStreamData aiStreamData = new AIStreamData();
            aiStreamData.setText(item.text());
            aiStreamData.setFinishReason(item.finishReason() == null ? "stop" : item.finishReason().toString());
            return aiStreamData;
        });
        return fluxResult;
    }

}
