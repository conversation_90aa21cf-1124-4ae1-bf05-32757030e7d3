package cn.e71.ai.config;


import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import cn.e71.ai.service.GAIGeminiGenAIService;
import cn.e71.ai.service.GAIService;


@AutoConfiguration
public class GAIAutoConfigure {

    @Bean
    @ConditionalOnProperty(prefix = "gan.ai", name = "api-platform", havingValue = "gemini")
    public GAIService geminiAIService(GAIProperties gaiProperties){
        return new GAIGeminiGenAIService(gaiProperties);
    }

}
