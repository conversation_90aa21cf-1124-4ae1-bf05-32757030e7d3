<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.e71</groupId>
    <artifactId>ai</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>cn.e71</groupId>
  <artifactId>gan-business-ai-core</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <name>gan-business-ai-core</name>
  <description>ai core</description>
  <dependencies>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-kernel-log</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-tomcat</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
      <version>${spring.boot.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.genai</groupId>
      <artifactId>google-genai</artifactId>
      <version>1.13.0</version>
    </dependency>
    <dependency>
      <groupId>cn.e71</groupId>
      <artifactId>gan-kernel-test</artifactId>
      <version>${revision}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
