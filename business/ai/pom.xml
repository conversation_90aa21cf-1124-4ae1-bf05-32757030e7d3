<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.e71</groupId>
        <artifactId>gan-business</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>ai</artifactId>
    <version>${revision}</version>
    <name>ai</name>
    <description>调用各类大模型</description>
    <packaging>pom</packaging>
    <modules>
        <module>starter</module>
        <module>core</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-webflux</artifactId>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-kernel-test</artifactId>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-business-ai-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.e71</groupId>
                <artifactId>gan-business-ai-core</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>